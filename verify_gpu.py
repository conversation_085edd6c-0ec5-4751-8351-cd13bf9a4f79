#!/usr/bin/env python3
"""
GPU Verification Script for Dota 2 Predictor
Tests XGBoost CUDA functionality with RTX 40-series cards
"""

import sys
import numpy as np

def check_xgboost_gpu():
    """Test XGBoost GPU functionality"""
    try:
        import xgboost as xgb
        print(f"✅ XGBoost version: {xgb.__version__}")
        
        # Create dummy training data
        X = np.random.rand(1000, 50)  # Simulate match features
        y = np.random.randint(0, 2, 1000)  # Binary classification (win/loss)
        dtrain = xgb.DMatrix(X, label=y)
        
        # XGBoost parameters for GPU training
        params = {
            'objective': 'binary:logistic',
            'eval_metric': 'logloss',
            'device': 'cuda',
            'tree_method': 'hist',  # GPU-accelerated histogram method
            'max_depth': 6,
            'learning_rate': 0.1
        }
        
        print("\n🚀 Testing GPU training...")
        bst = xgb.train(params, dtrain, num_boost_round=50, verbose_eval=False)
        
        # Test prediction
        predictions = bst.predict(dtrain)
        accuracy = np.mean((predictions > 0.5) == y)
        
        print(f"✅ SUCCESS! XGBoost GPU training completed")
        print(f"📊 Dummy model accuracy: {accuracy:.2%}")
        print(f"🎮 Your RTX 4070 is ready for Dota 2 match prediction!")
        
        return True
        
    except ImportError:
        print("❌ XGBoost not installed")
        return False
    except Exception as e:
        print(f"❌ GPU training failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure NVIDIA drivers are installed")
        print("2. Check CUDA toolkit installation: mamba list cudatoolkit")
        print("3. Verify cuDNN: mamba list cudnn")
        return False

def check_cuda_availability():
    """Check CUDA availability"""
    try:
        import xgboost as xgb
        # Try to get GPU count
        print(f"🔍 Checking CUDA devices...")
        
        # This will show available devices
        import subprocess
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected:")
            # Extract GPU info from nvidia-smi
            lines = result.stdout.split('\n')
            for line in lines:
                if 'RTX' in line or 'GTX' in line:
                    print(f"   {line.strip()}")
        else:
            print("⚠️  nvidia-smi not available")
            
    except Exception as e:
        print(f"⚠️  Could not check CUDA devices: {e}")

def main():
    """Main verification function"""
    print("🎯 Dota 2 Predictor - GPU Environment Verification")
    print("=" * 50)
    
    # Check CUDA
    check_cuda_availability()
    print()
    
    # Test XGBoost GPU
    success = check_xgboost_gpu()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Environment setup complete! Ready for ML training.")
        print("\n📝 Next steps:")
        print("1. Run: python ml/create_model_match_predict.py")
        print("2. Start bot: python start.py")
    else:
        print("❌ Environment setup incomplete. Please fix GPU issues.")
        sys.exit(1)

if __name__ == "__main__":
    main()