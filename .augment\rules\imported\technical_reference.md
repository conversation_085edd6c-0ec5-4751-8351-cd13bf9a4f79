---
type: "manual"
---

# Dota 2 Predictor - Technical Reference for AI Coding Agent

## 🔍 Codebase Analysis Context

### Current Model Architecture
- **Primary Model**: `MainML` class in `ml/model.py` handles all prediction logic
- **Model Files**: Three XGBoost models stored as `.pkl` files in project root
- **Feature Engineering**: Handled in `structure/helpers.py` with data preparation functions
- **Data Validation**: Uses Pandera schemas in `dataset/schemas.py`

### Database Schema Understanding
- **Core Tables**: Matches, predictions, user interactions, model performance metrics
- **ORM Models**: Defined using SQLAlchemy declarative base
- **Migration Strategy**: Manual schema updates with version tracking
- **Connection Management**: Session-based with proper cleanup

## 🎯 Critical Implementation Rules

### Model Handling Rules
1. **Always check model file existence** before loading
2. **Use GPU parameters** for XGBoost training: `device='cuda', tree_method='hist'`
3. **Maintain model versioning** in filenames and metadata
4. **Implement fallback mechanisms** if GPU training fails
5. **Validate input features** match training data schema

### Database Operation Rules
1. **Use context managers** for all database sessions
2. **Implement proper error handling** with rollback on failures
3. **Validate data types** before database insertion
4. **Use parameterized queries** to prevent SQL injection
5. **Monitor connection pool** health and performance

### API Integration Rules
1. **Implement rate limiting** for OpenDota API (1 request/second)
2. **Use exponential backoff** for retry mechanisms
3. **Validate API responses** before processing
4. **Cache frequently accessed data** to reduce API calls
5. **Handle API downtime gracefully** with fallback data

### Bot Command Rules
1. **Validate user inputs** before processing
2. **Provide clear error messages** with helpful suggestions
3. **Use consistent emoji patterns** for response formatting
4. **Implement command cooldowns** to prevent spam
5. **Log all user interactions** for debugging and analytics

## 📋 Code Patterns and Templates

### XGBoost Model Training Template
```python
def train_xgboost_model(X_train, y_train, X_val, y_val):
    """Standard XGBoost training with GPU support"""
    params = {
        'objective': 'binary:logistic',
        'eval_metric': 'logloss',
        'device': 'cuda',
        'tree_method': 'hist',
        'max_depth': 6,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8
    }
    
    dtrain = xgb.DMatrix(X_train, label=y_train)
    dval = xgb.DMatrix(X_val, label=y_val)
    
    model = xgb.train(
        params,
        dtrain,
        num_boost_round=1000,
        evals=[(dtrain, 'train'), (dval, 'val')],
        early_stopping_rounds=50,
        verbose_eval=100
    )
    
    return model
```

### Database Session Template
```python
from contextlib import contextmanager

@contextmanager
def get_db_session():
    """Database session context manager"""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()
```

### API Request Template
```python
import time
from functools import wraps

def rate_limit(calls_per_second=1):
    """Rate limiting decorator"""
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator

@rate_limit(calls_per_second=1)
def fetch_opendota_match(match_id):
    """Fetch match data with rate limiting"""
    response = requests.get(f"https://api.opendota.com/api/matches/{match_id}")
    response.raise_for_status()
    return response.json()
```

## 🛠️ Development Workflow Patterns

### Model Update Workflow
1. **Backup existing models** before replacement
2. **Validate new model performance** against test dataset
3. **Update model metadata** with training date and performance metrics
4. **Test prediction pipeline** with new model
5. **Monitor production performance** after deployment

### Feature Addition Workflow
1. **Define feature schema** in `dataset/schemas.py`
2. **Implement feature extraction** in `structure/helpers.py`
3. **Update model training scripts** to include new features
4. **Retrain all affected models** with new feature set
5. **Update prediction endpoints** to handle new features

### Database Migration Workflow
1. **Create migration script** with version number
2. **Test migration** on development database
3. **Backup production database** before migration
4. **Apply migration** with rollback plan
5. **Validate data integrity** after migration

## 🔧 Debugging and Monitoring

### Logging Patterns
```python
import logging

# Configure logging for each module
logger = logging.getLogger(__name__)

# Log levels for different scenarios
logger.debug("Detailed debugging information")
logger.info("General information about program execution")
logger.warning("Something unexpected happened")
logger.error("A serious error occurred")
logger.critical("A very serious error occurred")
```

### Performance Monitoring
- **GPU Usage**: Monitor with `nvidia-smi` during training
- **Memory Usage**: Track Python memory consumption
- **Database Performance**: Log slow queries and connection pool status
- **API Response Times**: Monitor external API call latencies
- **Bot Response Times**: Track command processing duration

### Error Handling Patterns
```python
def safe_prediction(model, features):
    """Safe prediction with comprehensive error handling"""
    try:
        # Validate input features
        if not validate_features(features):
            raise ValueError("Invalid feature format")
        
        # Make prediction
        prediction = model.predict(features)
        
        # Validate output
        if not validate_prediction(prediction):
            raise ValueError("Invalid prediction output")
        
        return prediction
        
    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        # Return default/fallback prediction
        return get_fallback_prediction()
```

## 📊 Testing Strategies

### Unit Testing Patterns
- **Model Testing**: Test prediction accuracy on known datasets
- **Database Testing**: Test CRUD operations with test database
- **API Testing**: Mock external API calls for consistent testing
- **Bot Testing**: Test command parsing and response formatting

### Integration Testing
- **End-to-End Prediction**: Test complete prediction pipeline
- **Database Integration**: Test model-database interaction
- **API Integration**: Test with real API calls (rate-limited)
- **Bot Integration**: Test complete bot command workflows

### Performance Testing
- **Model Performance**: Benchmark prediction speed and accuracy
- **Database Performance**: Test query performance under load
- **Memory Usage**: Monitor memory consumption during operations
- **GPU Utilization**: Verify efficient GPU usage during training

This technical reference provides specific implementation guidance for maintaining code quality, performance, and reliability in the Dota 2 Match Result Predictor project.
